import { DuckDBInstance } from '@duckdb/node-api';
import settings from '../settings';

const instance = await DuckDBInstance.create(settings.db.analyze.url);

const analyzeDb = await instance.connect();


// for (const symbol of SymbolList) {
//     await analyzeDb.run(`CREATE TABLE IF NOT EXISTS agg_trade_${symbol} (
//         syncId BIGINT NOT NULL UNIQUE,
//         aggId BIGINT PRIMARY KEY,
//         price DOUBLE NOT NULL,
//         qty DOUBLE NOT NULL,
//         time BIGINT NOT NULL,
//         isMaker BIGINT NOT NULL,
//     );`)
// }



export { analyzeDb };

import { db } from "./sqlite";
import { TNewTradeOrder, TTradeOrder, EOrderSide, EPositionSide } from "../types/tradeOrder.types";


export function findOpenPosition(symbol: string) {
    const query = `
        SELECT * FROM trade_order
        WHERE symbol = ?
        ORDER BY createdAt DESC
        LIMIT 1
    `;
    const order = db.query(query).get(symbol) as TTradeOrder
    if (!order) return null
    if (order.isClose) return null
    return {
        symbol: order.symbol,
        side: order.side === EOrderSide.BUY ? EPositionSide.LONG : EPositionSide.SHORT
    }

}

export function insertOrder(newOrder: TNewTradeOrder) {
    const queryStr = `INSERT INTO trade_order (symbol, orderId, side, isClose, createdAt) VALUES (?, ?,?, ?, ?)`
    db.run(queryStr, [newOrder.symbol, newOrder.orderId, newOrder.side, newOrder.isClose ?? 0, Date.now()])
}
import { type ScriptContext } from "lem-script"

const defaultInputs = { useTR: false, atrLen: 14, reversalB: 3 }

export default function weis(this: ScriptContext, params: typeof defaultInputs = defaultInputs) {
    this.overlay = false
    const lastZig = this.num(NaN)
    const boxSize = this.num(NaN)
    const trend = this.num(0)
    const waveVol = this.num(0)

    if (this.isFirst) {
        lastZig.v = this.close.v
        boxSize.v = this.f.atr(params.atrLen).v
    }

    let isNewWave = false
    if (this.f.crossUp(this.close, this.num(lastZig.v + boxSize.v * params.reversalB))) {
        isNewWave = true
        trend.v = 1
    } else if (this.f.crossDown(this.close, this.num(lastZig.v - boxSize.v * params.reversalB))) {
        isNewWave = true
        trend.v = -1
    }

    if (isNewWave) {
        boxSize.v = this.f.atr(params.atrLen).v
        lastZig.v = this.close.v
        waveVol.v = this.volume.v
    } else {
        waveVol.v += this.volume.v
    }

    this.plot({ series: waveVol, name: 'waveVol', color: trend.v === 1 ? '#64f781ff' : '#ff4d00ac' })
}

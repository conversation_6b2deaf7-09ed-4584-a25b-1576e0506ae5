
export enum EPositionSide {
    LONG = 'long',
    SHORT = 'short'
}

export enum EOrderSide {
    BUY = 'buy',
    SELL = 'sell'
}


export interface TTradeOrder {
    id: number;
    symbol: string;
    orderId: string;
    side: EOrderSide;
    isClose: number;
    createdAt: number;
}

export interface TNewTradeOrder {
    symbol: string;
    orderId: string;
    side: EOrderSide;
    isClose?: number;
}


export interface TOpenPosition {
    symbol: string;
    side: EPositionSide;
}


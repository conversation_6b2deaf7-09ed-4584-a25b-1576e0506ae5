import AdmZip from 'adm-zip'; // Import the library
import { SymbolList } from "../common/constants";
import <PERSON> from 'papaparse';
import { insertAggTrades } from '../database/aggTrade';

async function downloadAndUnzip(symbol, date) {
    try {
        // URL of the file to download
        const url = `https://data.binance.vision/data/spot/daily/aggTrades/${symbol}/${symbol}-aggTrades-${date}.zip`;
        const outputFileName = __dirname + `/${symbol}-aggTrades-${date}.csv`;

        console.log(`Downloading ${url}...`);
        // Download the file
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to download: ${response.statusText}`);
        }

        // Get the buffer of the zip file
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);

        // Create an AdmZip instance from the buffer
        const zip = new AdmZip(buffer);

        // Get all entries from the zip file
        const zipEntries = zip.getEntries();

        if (zipEntries.length === 0) {
            throw new Error('No entries found in the zip file.');
        }

        // Assuming there's only one CSV file inside the zip, or you want the first one
        // You might need more sophisticated logic if there are multiple files or specific naming conventions
        const csvEntry = zipEntries.find(entry => entry.entryName.endsWith('.csv'));

        if (!csvEntry) {
            throw new Error('No CSV file found inside the zip archive.');
        }

        // Get the data of the CSV entry as a buffer
        const csvBuffer = csvEntry.getData();

        // Write the CSV buffer to a file
        const file = Bun.file(outputFileName);
        await file.write(csvBuffer);

        console.log(`File downloaded and unzipped successfully to ${outputFileName}!`);

    } catch (error) {
        console.error('Error:', error.message);
    }
}


function csvToObjectArray(csvString) {
    const results = Papa.parse(csvString, {
        header: false,         // Set to false as Binance CSVs typically don't have a header row
        dynamicTyping: true,   // Attempt to convert values to appropriate types (numbers, booleans)
        skipEmptyLines: true   // Skip any empty lines in the CSV
    });

    if (results.errors.length > 0) {
        console.error('CSV Parsing Errors:', results.errors);
    }

    // Map the raw parsed data to your desired SQLite schema
    const mappedData = results.data.map(row => {
        // Ensure row has enough columns to prevent errors
        if (row.length < 8) { // Based on the 8 columns in Binance aggTrades data
            console.warn("Skipping row due to insufficient columns:", row);
            return null; // Return null for invalid rows, then filter them out
        }

        // Map data to the SQLite column names and types
        return {
            aggId: row[0],         // Binance's 'a' (Aggregate Trade ID) -> INTEGER
            price: row[1],         // Binance's 'p' (Price) -> REAL
            qty: row[2],           // Binance's 'q' (Quantity) -> REAL
            time: row[5],          // Binance's 'T' (Timestamp in milliseconds) -> INTEGER
            isMaker: row[6] === 'True' ? 1 : 0 // Binance's 'm' (Is Buyer Maker: boolean) -> INTEGER (0 or 1)
            // 'id' is AUTOINCREMENT, 'f' (First Trade ID), 'l' (Last Trade ID), and
            // 'M' (Is Best Price Match) are not included in this mapping based on your schema.
        };
    }).filter(Boolean); // Filter out any null entries from skipped rows

    return mappedData;
}

// for (const symbol of SymbolList) {
const symbol = 'hbar'
const file = Bun.file(__dirname + `/${(symbol + 'usdt').toUpperCase()}-aggTrades-2025-06-16.csv`)
const buf = await file.arrayBuffer()
const csvString = new TextDecoder().decode(buf)
const list = csvToObjectArray(csvString)
const result = insertAggTrades(symbol, list)
console.log(symbol, result.inserted)

for (let i = 0; i < 3; i++) {
    const date = new Date((new Date('2025-06-18')).getTime() - i * 24 * 60 * 60 * 1000).toISOString().slice(0, 10)
    await downloadAndUnzip((symbol + 'usdt').toUpperCase(), date)
}
// }


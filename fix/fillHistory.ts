import { fetchKlines } from "../exchange/binance";
import { upsertOhlcv } from "../database/ohlcv";
import { SymbolList, TimeframeList } from "../common/constants";
import { format } from "../pipeline/ohlcv/stages";

// const symbol = 'aaveusdt'
// const timeframe = '30m'
const lookback = 20 * 24 * 60 * 60 * 1000

for (const symbol of SymbolList) {
    for (const timeframe of ['15m']) {
        let time = Date.now()
        while (true) {
            const dataList = await fetchKlines({
                symbol: symbol,
                timeframe,
                endTime: time,
                limit: 1000,
            })
            for (const data of dataList) {
                const formatted = await format({
                    type: 'api',
                    extra: { symbol, timeframe },
                    data
                })
                await upsertOhlcv(symbol, timeframe, formatted!.data)
            }
            console.log(symbol, timeframe, `${dataList.length} candles inserted`, time)
            if (dataList.length < 1000 || time < Date.now() - lookback) break // Example condition (3 days ago)
            time = dataList[0][0] - 1
        }
    }
}

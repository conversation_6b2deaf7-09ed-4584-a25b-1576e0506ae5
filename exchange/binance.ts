import qs from 'qs'
import settings from '../settings'
import { retry } from 'radash'

async function callApi(path: string, params?: { method?: string, body?: any, query?: any, proxy?: string }) {
    const query = params?.query ? qs.stringify(params.query) : ''
    const url = `${settings.exchange.binance.apiUrl}${path}${query ? `?${query}` : ''}`

    const res = await fetch(url, {
        method: params?.method || 'GET',
        body: params?.body ? JSON.stringify(params.body) : undefined,
        headers: {
            'Content-Type': 'application/json'
        },
        proxy: params?.proxy,
        signal: AbortSignal.timeout(5000),
    })
    if (res.status !== 200) {
        const text = await res.text()
        throw new Error(`${path}  ${text}`)
    } else {
        const data = await res.json()
        return data
    }
}

export async function fetchKlines(params: { symbol: string, timeframe: string, limit?: number, endTime?: number, startTime?: number }) {
    const path = '/api/v3/klines'
    const res = await retry({ times: 3, delay: 500 }, async () => {
        return callApi(path, {
            query: {
                symbol: params.symbol.toUpperCase() + 'USDT',
                interval: params.timeframe,
                limit: params.limit,
                endTime: params.endTime,
                startTime: params.startTime
            },
            proxy: settings.proxy
        })
    })
    return res
}


export async function fetchAggTrades(params: {
    symbol: string;
    startId?: number;
    endId?: number;
    startTime?: number;
    endTime?: number;
    onData?: (data: any[]) => void;
}) {
    const tradeList: { aggId: number; price: number; qty: number; time: number }[] = [];
    const defaultLimit = 1000
    if (params.startId) {
        const path = '/api/v3/aggTrades';
        let startId = params.startId
        let endId = params.endId

        while (true) {
            const result = await retry({ times: 3, delay: 2000 }, async () => {
                return callApi(path, {
                    query: {
                        symbol: params.symbol.toUpperCase().replace('usdt', '') + 'USDT',
                        fromId: startId,
                        limit: endId ? endId - startId + 1 : defaultLimit,
                    },
                });
            })
            tradeList.push(...result);
            params.onData?.(result)
            startId = result[result.length - 1].a + 1;
            if (endId && startId >= endId) break
            if (!endId && result.length < defaultLimit) break
        }
        return tradeList;
    } else if (params.startTime && params.endTime) {
        let currentStartTime = params.startTime;
        while (currentStartTime < params.endTime) {
            const path = '/api/v3/aggTrades';
            const result = await retry({ times: 3, delay: 500 }, async () => {
                return callApi(path, {
                    query: {
                        symbol: params.symbol.toUpperCase().replace('usdt', '') + 'USDT',
                        startTime: currentStartTime,
                        endTime: params.endTime,
                        limit: defaultLimit,
                    },
                });
            })
            tradeList.push(...result);

            if (result.length < defaultLimit) {
                break;
            }
            currentStartTime = result[result.length - 1].T + 1;
        }
        return tradeList;
    }
    throw new Error(`invalid params ${JSON.stringify(params)}`)
}
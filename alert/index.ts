import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>rig<PERSON>, TAlertConfig } from "../types/alertConfig.types";
import { type AlertArgReader } from "./argReader";
import cache from "../common/cache";

export class Alert {
    private alertConfig: TAlertConfig;
    private argReader: AlertArgReader;

    constructor(alertConfig: TAlertConfig, argReader: AlertArgReader) {
        this.alertConfig = alertConfig;
        this.argReader = argReader
    }

    static async isTriggable(alertConfig: TAlertConfig, startTime: number, endTime: number) {
        switch (alertConfig.triggerType) {
            case EAlertTrigger.BeforeClosed: {
                const diff = endTime - Date.now()
                return diff >= 0 && diff <= 20 * 1000
            }
            case EAlertTrigger.Once: {
                const key = `alert:${alertConfig.id}`;
                if (!(cache.getValues(key))) {
                    return true;
                }
                return false;
            }
            case EAlertTrigger.OncePerCandle: {
                const key = `alert:${alertConfig.id}:${startTime.toString()}`;
                if (!cache.exists(key)) {
                    return true;
                }
                return false;
            }
            default:
                return false;
        }
    }


    evalute() {
        const { op } = this.alertConfig
        const data1 = this.argReader.get(this.alertConfig.arg1);
        const data2 = this.argReader.get(this.alertConfig.arg2);
        switch (op.type) {
            case EAlertOp.Cross:
                return this.cross(data1 as number[], data2 as number[])
            case EAlertOp.CrossUp:
                return this.crossUp(data1 as number[], data2 as number[])
            case EAlertOp.CrossDown:
                return this.crossDown(data1 as number[], data2 as number[])
            case EAlertOp.CrossChannel:
                return this.crossChannel(data1 as number[], data2 as number[][])
            case EAlertOp.EnterChannel:
                return this.enterChannel(data1 as number[], data2 as number[][])
            case EAlertOp.ExitChannel:
                return this.exitChannel(data1 as number[], data2 as number[][])
            default:
                return false
        }
    }

    cross(data1: number[], data2: number[]) {
        if (data1.length !== data2.length || data1.length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2;
        return ((y1 - y3) * (y2 - y4)) <= 0;
    }

    crossUp(data1: number[], data2: number[]) {
        if (data1.length !== data2.length || data1.length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2;
        return (y1 - y3) <= 0 && (y2 - y4) >= 0;
    }
    crossDown(data1: number[], data2: number[]) {
        if (data1.length !== data2.length || data1.length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2;
        return (y1 - y3) >= 0 && (y2 - y4) <= 0;
    }

    crossChannel(data1: number[], data2: number[][]) {
        if (data1.length !== 2 || data2.length !== 2 || data2[0].length !== 2 || data2[1].length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2[0];
        const [y5, y6] = data2[1];
        return ((y1 - y3) * (y2 - y4)) <= 0 || ((y1 - y5) * (y2 - y6)) <= 0;
    }
    enterChannel(data1: number[], data2: number[][]) {
        if (data1.length !== 2 || data2.length !== 2 || data2[0].length !== 2 || data2[1].length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2[0];
        const [y5, y6] = data2[1];
        return ((y1 - y3) * (y2 - y4)) > 0 && ((y1 - y5) * (y2 - y6)) > 0;
    }
    exitChannel(data1: number[], data2: number[][]) {
        if (data1.length !== 2 || data2.length !== 2 || data2[0].length !== 2 || data2[1].length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2[0];
        const [y5, y6] = data2[1];
        return ((y1 - y3) * (y2 - y4)) <= 0 || ((y1 - y5) * (y2 - y6)) <= 0;
    }
}
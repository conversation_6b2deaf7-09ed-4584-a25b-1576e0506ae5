import { TAlertArg } from "../types/alertConfig.types";
import { EDrawing, TDrawingConfig } from "../types/drawingConfig.types";
import { TPlotData } from "lem-script";


export class AlertArgReader {
    private plotDataList: TPlotData[];
    private drawingConfigList: TDrawingConfig[];
    private timeList: number[];

    constructor(timeList: number[], plotDataList: TPlotData[], drawingConfigList: TDrawingConfig[]) {
        this.plotDataList = plotDataList;
        this.drawingConfigList = drawingConfigList;
        this.timeList = timeList;
    }

    get(arg?: TAlertArg): number[] | number[][] {
        if (!arg) return [];
        if (arg.type === 'drawing') {
            return this.readDrawing(arg);
        }
        else if (arg.type === 'plot') {
            return this.readPlot(arg);
        } else if (arg.type === 'value') {
            return this.readValue(arg);
        }
        return []
    }

    private readDrawing(arg: TAlertArg) {
        const drawingConfig = this.drawingConfigList.find(d => d.id.toString() === arg.refId);
        if (!drawingConfig) return [];
        switch (drawingConfig.type) {
            case EDrawing.Ray:
                return this.readRay(drawingConfig);
            case EDrawing.HorizontalRayChannel:
                return this.readHorizontalRayChannel(drawingConfig);
            default:
                return []
        }
    }

    private readValue(arg: TAlertArg) {
        return this.timeList.map(time => arg.value!)
    }

    private readPlot(arg: TAlertArg): number[] {
        const plotData = this.plotDataList.find(p => p.id === arg.refId);
        if (!plotData) return [];

        switch (plotData.type) {
            case 'candlestick': {
                let result: number[] = []
                const plotDataSlice = plotData.data.slice(-2)
                for (let i = 0; i < plotDataSlice.length; i++) {
                    if (this.timeList[i] !== plotDataSlice[i].time) return []
                    result.push(plotDataSlice[i].close)
                }
                return result
            }

            case 'line': {
                let result: number[] = []
                const plotDataSlice = plotData.data.slice(-2)
                for (let i = 0; i < plotDataSlice.length; i++) {
                    if (this.timeList[i] !== plotDataSlice[i].time) return []
                    result.push(plotDataSlice[i].value)
                }
                return result
            }
            default:
                return []
        }

    }

    private readRay(drawingConfig: TDrawingConfig) {
        const [p1, p2] = drawingConfig.dataPoints;
        const slope = (p2.value - p1.value) / (p2.time - p1.time);
        const intercept = p1.value - slope * p1.time;
        return this.timeList.map(time => slope * time + intercept)
    }

    private readHorizontalRayChannel(drawingConfig: TDrawingConfig) {
        const [p1, p2] = drawingConfig.dataPoints;
        return [this.timeList.map(time => p1.value), this.timeList.map(time => p2.value)]
    }
}
